import { useState, useEffect } from 'react';
import { useStore } from '@nanostores/react';
import { userStore, userRoleInfoStore, roleDetectionLoadingStore } from '../../lib/state.ts';
import { detectUserRoles, getDashboardFeatures } from '../../lib/roleUtils.ts';
import RenterDashboard from './RenterDashboard.tsx';
import OwnerDashboard from './OwnerDashboard.tsx';
import AdminDashboard from '../admin/AdminDashboard.tsx';
import RoleSwitcher, { useRoleSwitcher } from './RoleSwitcher.tsx';
import type { UserRole } from '../../lib/roleUtils.ts';

// Extended role type to include combined view
type DashboardView = UserRole | 'combined';

interface UnifiedDashboardProps {
  className?: string;
}

export default function UnifiedDashboard({ className = '' }: UnifiedDashboardProps) {
  const user = useStore(userStore);
  const roleInfo = useStore(userRoleInfoStore);
  const roleDetectionLoading = useStore(roleDetectionLoadingStore);
  const [isInitializing, setIsInitializing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize role switcher with user's primary role
  const { currentRole, switchRole, getPreferredRole } = useRoleSwitcher(
    roleInfo?.primaryRole || 'renter'
  );

  // State for dashboard view (can be a role or 'combined')
  const [currentView, setCurrentView] = useState<DashboardView>(currentRole);

  useEffect(() => {
    if (user && !roleInfo && !roleDetectionLoading) {
      initializeRoles();
    }
  }, [user, roleInfo, roleDetectionLoading]);

  useEffect(() => {
    // Update current role when role info changes
    if (roleInfo && roleInfo.roles.length > 0) {
      const preferredRole = getPreferredRole(roleInfo.roles);
      if (preferredRole !== currentRole) {
        switchRole(preferredRole);
      }

      // Set initial view based on dashboard features
      const features = getDashboardFeatures(roleInfo);
      if (features.defaultView === 'combined') {
        setCurrentView('combined');
      } else {
        setCurrentView(preferredRole);
      }

      setIsInitializing(false);
    }
  }, [roleInfo]);

  const initializeRoles = async () => {
    if (!user) return;

    try {
      setError(null);
      const detectedRoles = await detectUserRoles(user);
      
      if (detectedRoles) {
        // Update global role info store
        userRoleInfoStore.set(detectedRoles);
        
        // Set preferred role
        const preferredRole = getPreferredRole(detectedRoles.roles);
        switchRole(preferredRole);
      } else {
        setError('Failed to detect user roles');
      }
    } catch (err) {
      console.error('Error initializing roles:', err);
      setError('Failed to initialize dashboard');
    } finally {
      setIsInitializing(false);
    }
  };

  // Loading state
  if (isInitializing || roleDetectionLoading || !user) {
    return (
      <div className={`${className}`}>
        <div className="animate-pulse">
          {/* Header skeleton */}
          <div className="mb-8">
            <div className="h-8 bg-slate-200 rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-slate-200 rounded w-1/2"></div>
          </div>
          
          {/* Stats skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                <div className="h-4 bg-slate-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-slate-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
          
          {/* Content skeleton */}
          <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
            <div className="h-6 bg-slate-200 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-16 bg-slate-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-xl p-6">
          <div className="flex items-center">
            <div className="text-red-500 mr-3">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-red-800 font-medium">Dashboard Error</h3>
              <p className="text-red-600 text-sm mt-1">{error}</p>
            </div>
          </div>
          <button
            type="button"
            onClick={initializeRoles}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!roleInfo) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-12">
          <p className="text-slate-600">Unable to load dashboard. Please refresh the page.</p>
        </div>
      </div>
    );
  }

  const dashboardFeatures = getDashboardFeatures(roleInfo);

  return (
    <div className={className}>
      {/* Role Switcher for dual-role users */}
      {dashboardFeatures.showRoleSwitcher && (
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-slate-900">Dashboard</h1>
              <p className="text-slate-600 text-sm mt-1">
                You have multiple roles. Switch between them to access different features.
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <RoleSwitcher
                availableRoles={roleInfo.roles}
                currentRole={currentRole}
                onRoleChange={(role) => {
                  switchRole(role);
                  setCurrentView(role);
                }}
              />
              {dashboardFeatures.defaultView === 'combined' && (
                <button
                  type="button"
                  onClick={() => setCurrentView(currentView === 'combined' ? currentRole : 'combined')}
                  className="px-3 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
                >
                  {currentView === 'combined' ? 'Split View' : 'Combined View'}
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Role-specific dashboard content */}
      {currentView === 'admin' && dashboardFeatures.showAdminFeatures && (
        <AdminDashboard />
      )}

      {currentView === 'owner' && dashboardFeatures.showOwnerFeatures && (
        <OwnerDashboard />
      )}

      {currentView === 'renter' && dashboardFeatures.showRenterFeatures && (
        <RenterDashboard />
      )}

      {/* Combined view for users who prefer to see everything */}
      {currentView === 'combined' && (
        <div className="space-y-12">
          {dashboardFeatures.showOwnerFeatures && (
            <div>
              <div className="flex items-center mb-6">
                <div className="p-2 bg-green-100 rounded-lg mr-3">
                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold text-slate-900">Venue Owner Dashboard</h2>
              </div>
              <OwnerDashboard />
            </div>
          )}
          
          {dashboardFeatures.showRenterFeatures && (
            <div>
              <div className="flex items-center mb-6">
                <div className="p-2 bg-blue-100 rounded-lg mr-3">
                  <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold text-slate-900">Renter Dashboard</h2>
              </div>
              <RenterDashboard />
            </div>
          )}
        </div>
      )}

      {/* Fallback for edge cases */}
      {!dashboardFeatures.showRenterFeatures && 
       !dashboardFeatures.showOwnerFeatures && 
       !dashboardFeatures.showAdminFeatures && (
        <div className="text-center py-12">
          <div className="text-slate-400 text-lg mb-2">Welcome to Trodoo!</div>
          <p className="text-slate-600 mb-6">
            Get started by browsing venues or listing your own space.
          </p>
          <div className="space-x-3">
            <a
              href="/venues"
              className="inline-flex items-center px-6 py-3 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 transition-colors"
            >
              Browse Venues
            </a>
            <a
              href="/venues/new"
              className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 transition-colors"
            >
              List Your Venue
            </a>
          </div>
        </div>
      )}
    </div>
  );
}

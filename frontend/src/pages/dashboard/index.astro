---
import BaseLayout from '../../layouts/BaseLayout.astro';
import Header from '../../components/core/Header.astro';


// Check if user is authenticated
const authCookie = Astro.cookies.get('pb_auth');
if (!authCookie) {
  return Astro.redirect('/auth/login');
}
---

<BaseLayout
  title="Dashboard - Trodoo"
  description="Manage your bookings, venues, and account settings."
>
<Header />
  <main class="min-h-screen bg-gradient-to-br from-slate-50 to-white">
    <!-- App Wrapper Container -->
    <div id="app-wrapper-container">
      <!-- Dashboard Header -->
      <div class="bg-white border-b border-slate-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-8">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-slate-900">Dashboard</h1>
              <p class="mt-2 text-slate-600">
                Welcome back! Here's what's happening with your account.
              </p>
            </div>
            <div class="flex items-center space-x-3">
              <a
                href="/venues/new"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
              >
                List Your Venue
              </a>
              <a
                href="/venues"
                class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
              >
                Browse Venues
              </a>

              <!-- Profile Dropdown -->
              <div id="profile-dropdown-container"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Review Prompts -->
      <div id="review-prompts-container" class="mb-6"></div>

      <!-- Welcome Message (for new users) -->
      <div id="welcome-banner" class="hidden mb-8 bg-primary-50 border border-primary-200 rounded-lg p-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-primary-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-primary-800">
              Welcome to Trodoo!
            </h3>
            <div class="mt-2 text-sm text-primary-700">
              <p>Your account has been created successfully. Start by browsing venues or listing your own space.</p>
            </div>
            <div class="mt-4">
              <div class="-mx-2 -my-1.5 flex">
                <button type="button" class="bg-primary-50 px-2 py-1.5 rounded-md text-sm font-medium text-primary-800 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-primary-50 focus:ring-primary-600" onclick="this.parentElement.parentElement.parentElement.parentElement.style.display='none'">
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Role-Based Dashboard Container -->
      <div id="unified-dashboard-container">
        <!-- UnifiedDashboard component will be mounted here -->
        <div class="text-center py-8 text-gray-500">
          Loading dashboard...
        </div>
      </div>


    </div>
    </div> <!-- End app-wrapper-container -->
  </main>


</BaseLayout>

<!-- React Components Integration -->
<script>
  import UnifiedDashboard from '../../components/dashboard/UnifiedDashboard.tsx';
  import ReviewPromptBanner from '../../components/dashboard/ReviewPromptBanner.tsx';
  import AppWrapper from '../../components/common/AppWrapper.tsx';
  import ProfileDropdown from '../../components/common/ProfileDropdown.tsx';
  import { createRoot } from 'react-dom/client';
  import React from 'react';
  import { userStore, initializeAuth, authLoadingStore } from '../../lib/state.ts';

  // Helper function to wait for authentication to complete
  function waitForAuth(): Promise<void> {
    return new Promise((resolve) => {
      const checkAuth = () => {
        if (!authLoadingStore.get()) {
          resolve();
        } else {
          setTimeout(checkAuth, 100);
        }
      };
      checkAuth();
    });
  }

  // Dashboard initialization
  document.addEventListener('DOMContentLoaded', async () => {
    // Initialize authentication state first
    initializeAuth();

    // Wait for auth to be ready
    await waitForAuth();

    // First, mount a simple test component to verify React is working
    const unifiedDashboardContainer = document.getElementById('unified-dashboard-container');
    if (unifiedDashboardContainer) {
      console.log('Dashboard - Mounting test component');
      const dashboardRoot = createRoot(unifiedDashboardContainer);

      // Simple test component with user debug info
      const TestComponent = () => {
        return React.createElement('div', {
          style: { padding: '20px', background: '#f0f0f0', borderRadius: '8px', fontFamily: 'monospace' }
        }, [
          React.createElement('h3', { key: 'title' }, 'React is working! Loading dashboard...'),
          React.createElement('p', { key: 'user-id' }, `User ID: ${user.id}`),
          React.createElement('p', { key: 'user-email' }, `Email: ${user.email}`),
          React.createElement('p', { key: 'user-roles' }, `Roles: ${JSON.stringify(user.roles)}`),
          React.createElement('p', { key: 'user-full' }, `Full User: ${JSON.stringify(user, null, 2)}`)
        ]);
      };

      dashboardRoot.render(React.createElement(TestComponent));

      // After a short delay, mount the actual UnifiedDashboard
      setTimeout(() => {
        console.log('Dashboard - Mounting UnifiedDashboard component');
        dashboardRoot.render(React.createElement(UnifiedDashboard));
      }, 1000);
    } else {
      console.error('Dashboard - unified-dashboard-container not found');
    }

    // Mount AppWrapper component to wrap the entire dashboard content
    const appWrapperContainer = document.getElementById('app-wrapper-container');
    if (appWrapperContainer) {
      const originalContent = appWrapperContainer.innerHTML;
      const root = createRoot(appWrapperContainer);
      root.render(React.createElement(AppWrapper, {
        children: React.createElement('div', { dangerouslySetInnerHTML: { __html: originalContent } })
      }));

      // Wait for React to render the new DOM, then mount ProfileDropdown
      setTimeout(() => {
        const profileDropdownContainer = document.getElementById('profile-dropdown-container');
        if (profileDropdownContainer) {
          const profileRoot = createRoot(profileDropdownContainer);
          profileRoot.render(React.createElement(ProfileDropdown));
        }
      }, 100);
    }



    // Mount ReviewPromptBanner component
    const reviewPromptsContainer = document.getElementById('review-prompts-container');
    if (reviewPromptsContainer) {
      const root = createRoot(reviewPromptsContainer);
      root.render(React.createElement(ReviewPromptBanner));
    }

    // Check for welcome parameter
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('welcome') === 'true') {
      const welcomeBanner = document.getElementById('welcome-banner');
      if (welcomeBanner) {
        welcomeBanner.classList.remove('hidden');
      }
    }

    // Get current user from state
    const user = userStore.get();
    if (!user) {
      console.error('No authenticated user found');
      globalThis.location.href = '/auth/login';
      return;
    }

    console.log('Dashboard - User authenticated:', user.id, 'Email:', user.email, 'Roles:', user.roles, 'Full user object:', user);
  });
</script>


